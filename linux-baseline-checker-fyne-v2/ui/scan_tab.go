package ui

import (
	"fmt"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"lcheck/core"
	"lcheck/data"
)



// ScanTab 扫描选项卡
type ScanTab struct {
	storage      *data.Storage
	scanner      *core.Scanner
	updateStatus func(string)
	window       fyne.Window
	app          *App  // 添加应用实例引用

	// UI组件
	container     *fyne.Container
	taskList      *widget.List

	// 工具栏按钮
	createTaskBtn *widget.Button
	editTaskBtn   *widget.Button
	deleteTaskBtn *widget.Button
	startStopBtn  *widget.Button
	detailBtn     *widget.Button

	// 数据
	hosts     []data.HostInfo
	groups    []data.HostGroup
	tasks     []data.ScanTask
	selectedTask *data.ScanTask
}

// NewScanTab 创建扫描选项卡
func NewScanTab(storage *data.Storage, scanner *core.Scanner, updateStatus func(string)) *ScanTab {
	tab := &ScanTab{
		storage:      storage,
		scanner:      scanner,
		updateStatus: updateStatus,
		hosts:        make([]data.HostInfo, 0),
		groups:       make([]data.HostGroup, 0),
		tasks:        make([]data.ScanTask, 0),
	}

	tab.initializeComponents()
	tab.loadTasks()
	return tab
}

// SetWindow 设置窗口引用
func (s *ScanTab) SetWindow(window fyne.Window) {
	s.window = window
}

// SetApp 设置应用引用
func (s *ScanTab) SetApp(app *App) {
	s.app = app
}

// initializeComponents 初始化组件
func (s *ScanTab) initializeComponents() {
	// 创建文字工具栏按钮
	s.createTaskBtn = widget.NewButton("创建", func() {
		fmt.Printf("点击创建任务按钮\n")
		s.showCreateTaskDialog()
	})
	s.editTaskBtn = widget.NewButton("编辑", s.editSelectedTask)
	s.deleteTaskBtn = widget.NewButton("删除", s.deleteSelectedTask)
	s.startStopBtn = widget.NewButton("启动", s.toggleSelectedTask)
	s.detailBtn = widget.NewButton("详情", s.showTaskDetail)

	// 初始状态禁用部分按钮
	s.editTaskBtn.Disable()
	s.deleteTaskBtn.Disable()
	s.startStopBtn.Disable()
	s.detailBtn.Disable()

	// 创建任务列表
	s.taskList = widget.NewList(
		func() int {
			return len(s.tasks)
		},
		func() fyne.CanvasObject {
			// 创建紧凑的任务项
			nameLabel := widget.NewLabel("任务名称")
			nameLabel.TextStyle = fyne.TextStyle{Bold: true}

			statusLabel := widget.NewLabel("状态")
			progressBar := widget.NewProgressBar()
			progressBar.Resize(fyne.NewSize(80, 16)) // 适中进度条

			exportBtn := widget.NewButton("导出", func() {})
			exportBtn.Resize(fyne.NewSize(45, 22)) // 适中按钮

			// 使用超紧凑的HBox布局
			taskItem := container.NewHBox(
				nameLabel,
				widget.NewLabel("·"),
				statusLabel,
				widget.NewLabel("·"),
				progressBar,
				exportBtn,
			)

			return taskItem
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id < len(s.tasks) {
				task := s.tasks[id]
				hbox := obj.(*fyne.Container)

				// HBox结构: 0: nameLabel, 1: separator, 2: statusLabel, 3: separator, 4: progressBar, 5: exportBtn
				nameLabel := hbox.Objects[0].(*widget.Label)
				statusLabel := hbox.Objects[2].(*widget.Label)
				progressBar := hbox.Objects[4].(*widget.ProgressBar)
				exportBtn := hbox.Objects[5].(*widget.Button)

				nameLabel.SetText(task.Name)
				statusLabel.SetText(task.Status)
				progressBar.SetValue(task.Progress)

				// 更新导出按钮回调
				exportBtn.OnTapped = func() {
					s.exportTask(task)
				}
			}
		},
	)

	// 设置任务列表选择回调
	s.taskList.OnSelected = func(id widget.ListItemID) {
		fmt.Printf("选中任务: ID=%d\n", id)
		if id < len(s.tasks) {
			s.selectedTask = &s.tasks[id]
			fmt.Printf("选中任务: %s\n", s.selectedTask.Name)
			s.updateButtonStates()
		}
	}

	s.taskList.OnUnselected = func(id widget.ListItemID) {
		fmt.Printf("取消选中任务: ID=%d\n", id)
		s.selectedTask = nil
		s.updateButtonStates()
	}
}

// Create 创建选项卡内容
func (s *ScanTab) Create() fyne.CanvasObject {
	// 创建任务管理面板，并设置为可扩展
	taskPanel := s.createTaskPanel()

	// 使用Border布局确保任务面板占满整个可用空间
	s.container = container.NewBorder(
		nil, nil, nil, nil, // 四边都不固定
		taskPanel, // 中心内容自动扩展
	)

	return s.container
}

// createTaskPanel 创建任务面板
func (s *ScanTab) createTaskPanel() fyne.CanvasObject {
	// 创建工具栏
	toolbar := container.NewHBox(
		s.createTaskBtn,
		s.editTaskBtn,
		s.deleteTaskBtn,
		s.startStopBtn,
		s.detailBtn,
	)

	// 创建任务列表滚动容器
	taskListScroll := container.NewScroll(s.taskList)

	// 创建任务列表容器，让列表自动扩展填充可用空间
	taskListContainer := container.NewBorder(
		toolbar,         // 顶部工具栏
		nil,             // 底部
		nil,             // 左侧
		nil,             // 右侧
		taskListScroll,  // 中心内容，自动扩展
	)

	return taskListContainer
}

// showCreateTaskDialog 显示创建任务对话框
func (s *ScanTab) showCreateTaskDialog() {
	fmt.Printf("显示创建任务对话框\n")

	// 实时获取最新的主机和主机组数据
	s.refreshHostData()
	fmt.Printf("主机数量: %d, 主机组数量: %d\n", len(s.hosts), len(s.groups))

	// 检查是否有主机数据
	if len(s.hosts) == 0 && len(s.groups) == 0 {
		dialog.ShowInformation("提示", "请先在主机管理中添加主机信息或创建主机组", s.window)
		return
	}

	// 创建表单组件
	taskNameEntry := widget.NewEntry()
	taskNameEntry.SetPlaceHolder("请输入任务名称")

	// 创建主机/主机组选择器
	var targetOptions []string

	// 添加单个主机选项
	for _, host := range s.hosts {
		targetOptions = append(targetOptions, fmt.Sprintf("主机: %s (%s)", host.Name, host.Host))
	}

	// 添加主机组选项
	for _, group := range s.groups {
		hostCount := len(group.Hosts)
		targetOptions = append(targetOptions, fmt.Sprintf("主机组: %s (%d台主机)", group.Name, hostCount))
	}

	targetSelect := widget.NewSelect(targetOptions, nil)
	if len(targetOptions) > 0 {
		targetSelect.SetSelected(targetOptions[0]) // 默认选择第一个选项
	}

	// 创建基线检查模板选择器
	templateOptions := []string{
		"完整安全检查",
		"账户安全检查",
		"SSH安全检查",
		"网络安全检查",
		"系统安全检查",
		"文件系统安全检查",
		"日志审计检查",
	}
	templateSelect := widget.NewSelect(templateOptions, nil)
	templateSelect.SetSelected("完整安全检查")

	// 创建表单
	form := container.NewVBox(
		widget.NewLabel("任务名称:"),
		taskNameEntry,
		widget.NewSeparator(),
		widget.NewLabel("选择目标 (主机或主机组):"),
		targetSelect,
		widget.NewSeparator(),
		widget.NewLabel("选择基线检查模板:"),
		templateSelect,
	)

	// 创建对话框
	dialog := dialog.NewCustomConfirm(
		"创建任务",
		"确定",
		"取消",
		form,
		func(confirmed bool) {
			fmt.Printf("对话框回调: confirmed=%v\n", confirmed)
			if confirmed {
				fmt.Printf("开始创建任务: name=%s, target=%s, template=%s\n",
					taskNameEntry.Text, targetSelect.Selected, templateSelect.Selected)
				s.createTask(taskNameEntry.Text, targetSelect.Selected, templateSelect.Selected)
			}
		},
		s.window,
	)

	dialog.Resize(fyne.NewSize(350, 250))
	dialog.Show()
}

// createTask 创建新任务
func (s *ScanTab) createTask(name, targetInfo, template string) {
	if name == "" {
		dialog.ShowError(fmt.Errorf("任务名称不能为空"), s.window)
		return
	}

	if targetInfo == "" {
		dialog.ShowError(fmt.Errorf("请选择目标主机或主机组"), s.window)
		return
	}

	var selectedHostIDs []string
	var taskType string

	// 判断选择的是主机还是主机组
	if strings.HasPrefix(targetInfo, "主机: ") {
		// 选择的是单个主机
		taskType = "单主机扫描"
		for _, host := range s.hosts {
			hostDisplay := fmt.Sprintf("主机: %s (%s)", host.Name, host.Host)
			if hostDisplay == targetInfo {
				selectedHostIDs = []string{host.ID}
				break
			}
		}
	} else if strings.HasPrefix(targetInfo, "主机组: ") {
		// 选择的是主机组
		taskType = "批量扫描"
		for _, group := range s.groups {
			groupDisplay := fmt.Sprintf("主机组: %s (%d台主机)", group.Name, len(group.Hosts))
			if groupDisplay == targetInfo {
				for _, host := range group.Hosts {
					selectedHostIDs = append(selectedHostIDs, host.ID)
				}
				break
			}
		}
	}

	if len(selectedHostIDs) == 0 {
		dialog.ShowError(fmt.Errorf("未找到选中的目标"), s.window)
		return
	}

	// 创建新任务
	task := data.ScanTask{
		ID:        fmt.Sprintf("task_%d", time.Now().Unix()),
		Name:      name,
		Status:    "待执行",
		Progress:  0.0,
		HostIDs:   selectedHostIDs,
		Template:  template,
		CreatedAt: time.Now(),
		Results:   make([]data.ScanResult, 0),
	}

	s.tasks = append(s.tasks, task)
	fmt.Printf("创建任务成功: %s, 任务总数: %d\n", task.Name, len(s.tasks))

	s.saveTasks()
	s.taskList.Refresh()

	// 自动选中新创建的任务
	newTaskIndex := len(s.tasks) - 1
	s.taskList.Select(newTaskIndex)
	s.selectedTask = &s.tasks[newTaskIndex]
	s.updateButtonStates()
	fmt.Printf("自动选中新创建的任务: %s (索引: %d)\n", task.Name, newTaskIndex)

	targetCount := len(selectedHostIDs)
	s.updateStatus(fmt.Sprintf("已创建任务: %s (%s, %d台主机)", name, taskType, targetCount))
}

// editSelectedTask 编辑选中的任务
func (s *ScanTab) editSelectedTask() {
	if s.selectedTask == nil {
		return
	}

	// 只允许编辑待执行状态的任务
	if s.selectedTask.Status != "待执行" {
		dialog.ShowInformation("提示", "只能编辑待执行状态的任务", s.window)
		return
	}

	s.updateStatus("编辑任务功能开发中...")
}

// deleteSelectedTask 删除选中的任务
func (s *ScanTab) deleteSelectedTask() {
	if s.selectedTask == nil {
		return
	}

	dialog.ShowConfirm(
		"确认删除",
		fmt.Sprintf("确定要删除任务 '%s' 吗？", s.selectedTask.Name),
		func(confirmed bool) {
			if confirmed {
				s.deleteTask(s.selectedTask.ID)
			}
		},
		s.window,
	)
}

// deleteTask 删除任务
func (s *ScanTab) deleteTask(taskID string) {
	fmt.Printf("删除任务: ID=%s, 删除前任务数量=%d\n", taskID, len(s.tasks))

	found := false
	for i, task := range s.tasks {
		if task.ID == taskID {
			fmt.Printf("找到要删除的任务: %s (索引: %d)\n", task.Name, i)
			s.tasks = append(s.tasks[:i], s.tasks[i+1:]...)
			found = true
			break
		}
	}

	if !found {
		fmt.Printf("警告: 未找到要删除的任务 ID: %s\n", taskID)
		return
	}

	fmt.Printf("删除后任务数量: %d\n", len(s.tasks))

	s.selectedTask = nil
	s.saveTasks()
	s.taskList.Refresh()
	s.updateButtonStates()
	s.updateStatus("任务已删除")
}

// toggleSelectedTask 启动/停止选中的任务
func (s *ScanTab) toggleSelectedTask() {
	if s.selectedTask == nil {
		return
	}

	switch s.selectedTask.Status {
	case "待执行":
		s.startTask(s.selectedTask.ID)
	case "运行中":
		s.stopTask(s.selectedTask.ID)
	default:
		dialog.ShowInformation("提示", "该任务无法启动或停止", s.window)
	}
}

// startTask 启动任务
func (s *ScanTab) startTask(taskID string) {
	// 找到要启动的任务
	var taskIndex = -1
	for i, task := range s.tasks {
		if task.ID == taskID {
			taskIndex = i
			break
		}
	}

	if taskIndex == -1 {
		s.updateStatus("未找到要启动的任务")
		return
	}

	// 更新任务状态
	s.tasks[taskIndex].Status = "运行中"
	now := time.Now()
	s.tasks[taskIndex].StartedAt = &now
	s.tasks[taskIndex].Progress = 0.0
	s.tasks[taskIndex].Error = ""

	// 更新选中任务的引用
	s.updateSelectedTaskReference(taskID)

	s.saveTasks()
	s.taskList.Refresh()
	s.updateButtonStates()
	s.updateStatus("任务已启动")

	// 启动后台扫描协程
	go s.executeTask(taskID)
}

// stopTask 停止任务
func (s *ScanTab) stopTask(taskID string) {
	for i, task := range s.tasks {
		if task.ID == taskID {
			s.tasks[i].Status = "已停止"

			// 更新选中任务的引用
			s.updateSelectedTaskReference(taskID)
			break
		}
	}

	s.saveTasks()
	s.taskList.Refresh()
	s.updateButtonStates()
	s.updateStatus("任务已停止")
}

// showTaskDetail 显示任务详情
func (s *ScanTab) showTaskDetail() {
	if s.selectedTask == nil {
		return
	}

	task := s.selectedTask

	// 创建详情内容
	content := container.NewVBox(
		widget.NewCard("基本信息", "", container.NewVBox(
			widget.NewLabel(fmt.Sprintf("任务名称: %s", task.Name)),
			widget.NewLabel(fmt.Sprintf("任务ID: %s", task.ID)),
			widget.NewLabel(fmt.Sprintf("状态: %s", task.Status)),
			widget.NewLabel(fmt.Sprintf("进度: %.1f%%", task.Progress*100)),
			widget.NewLabel(fmt.Sprintf("检查模板: %s", task.Template)),
			widget.NewLabel(fmt.Sprintf("创建时间: %s", task.CreatedAt.Format("2006-01-02 15:04:05"))),
		)),
	)

	// 添加时间信息
	if task.StartedAt != nil {
		content.Add(widget.NewCard("执行信息", "", container.NewVBox(
			widget.NewLabel(fmt.Sprintf("开始时间: %s", task.StartedAt.Format("2006-01-02 15:04:05"))),
		)))
	}

	if task.CompletedAt != nil {
		duration := task.CompletedAt.Sub(*task.StartedAt)
		content.Objects[len(content.Objects)-1].(*widget.Card).Content.(*fyne.Container).Add(
			widget.NewLabel(fmt.Sprintf("完成时间: %s", task.CompletedAt.Format("2006-01-02 15:04:05"))),
		)
		content.Objects[len(content.Objects)-1].(*widget.Card).Content.(*fyne.Container).Add(
			widget.NewLabel(fmt.Sprintf("执行时长: %s", duration.String())),
		)
	}

	// 添加目标主机信息
	hostInfo := "目标主机:\n"
	for i, hostID := range task.HostIDs {
		// 查找主机名称
		hostName := hostID
		for _, host := range s.hosts {
			if host.ID == hostID {
				hostName = fmt.Sprintf("%s (%s)", host.Name, host.Host)
				break
			}
		}
		hostInfo += fmt.Sprintf("%d. %s\n", i+1, hostName)
	}

	content.Add(widget.NewCard("目标信息", "", container.NewVBox(
		widget.NewLabel(hostInfo),
	)))

	// 添加错误信息（如果有）
	if task.Error != "" {
		content.Add(widget.NewCard("错误信息", "", container.NewVBox(
			widget.NewLabel(task.Error),
		)))
	}

	// 添加检查结果
	if len(task.Results) > 0 {
		resultsContent := s.formatCheckResults(task.Results)
		if resultsContent != "" {
			resultsEntry := widget.NewEntry()
			resultsEntry.MultiLine = true
			resultsEntry.Wrapping = fyne.TextWrapWord
			resultsEntry.SetText(resultsContent)
			resultsEntry.Resize(fyne.NewSize(340, 150))

			content.Add(widget.NewCard("检查结果", "", container.NewScroll(resultsEntry)))
		}
	}

	// 创建滚动容器 - 适配500×500窗口
	scroll := container.NewScroll(content)
	scroll.Resize(fyne.NewSize(380, 280))

	// 创建对话框 - 适配500×500窗口
	dialog := dialog.NewCustom(
		fmt.Sprintf("详情 - %s", task.Name),
		"关闭",
		scroll,
		s.window,
	)

	dialog.Resize(fyne.NewSize(400, 320))
	dialog.Show()
}

// formatCheckResults 格式化检查结果
func (s *ScanTab) formatCheckResults(results []data.ScanResult) string {
	if len(results) == 0 {
		return "暂无检查结果"
	}

	var output string
	for i, result := range results {
		output += fmt.Sprintf("=== 主机 %d: %s ===\n", i+1, result.HostName)
		output += fmt.Sprintf("扫描状态: %s\n", result.Status)
		output += fmt.Sprintf("总检查项: %d\n", result.TotalChecks)
		output += fmt.Sprintf("通过: %d, 警告: %d, 失败: %d\n",
			result.PassedChecks, result.WarningChecks, result.FailedChecks)

		if len(result.CheckResults) > 0 {
			output += "\n检查详情:\n"
			for _, check := range result.CheckResults {
				status := "✓"
				if check.Status == "警告" {
					status = "⚠"
				} else if check.Status == "失败" {
					status = "✗"
				}
				output += fmt.Sprintf("  %s %s (%s风险): %s\n",
					status, check.CheckName, check.Risk, check.Description)
			}
		}
		output += "\n"
	}

	return output
}

// executeTask 执行扫描任务
func (s *ScanTab) executeTask(taskID string) {
	fmt.Printf("开始执行任务: %s\n", taskID)

	// 找到任务
	var taskIndex = -1
	for i, task := range s.tasks {
		if task.ID == taskID {
			taskIndex = i
			break
		}
	}

	if taskIndex == -1 {
		fmt.Printf("未找到任务: %s\n", taskID)
		return
	}

	task := &s.tasks[taskIndex]

	// 模拟扫描过程
	totalSteps := len(task.HostIDs) * 10 // 每个主机10个检查步骤
	currentStep := 0

	for _, hostID := range task.HostIDs {
		// 检查任务是否被停止
		if s.tasks[taskIndex].Status != "运行中" {
			fmt.Printf("任务被停止: %s\n", taskID)
			return
		}

		// 查找主机信息
		var host *data.HostInfo
		for i := range s.hosts {
			if s.hosts[i].ID == hostID {
				host = &s.hosts[i]
				break
			}
		}

		if host == nil {
			fmt.Printf("未找到主机: %s\n", hostID)
			continue
		}

		fmt.Printf("开始扫描主机: %s (%s)\n", host.Name, host.Host)

		// 模拟扫描步骤
		for step := 1; step <= 10; step++ {
			// 检查任务是否被停止
			if s.tasks[taskIndex].Status != "运行中" {
				return
			}

			// 模拟扫描延时
			time.Sleep(500 * time.Millisecond)

			currentStep++
			progress := float64(currentStep) / float64(totalSteps)

			// 更新进度
			s.tasks[taskIndex].Progress = progress

			// 每隔几步更新一次UI
			if currentStep%3 == 0 {
				s.saveTasks()
				// 在主线程中更新UI
				go func() {
					s.taskList.Refresh()
				}()
			}

			fmt.Printf("任务 %s 进度: %.1f%%\n", taskID, progress*100)
		}

		fmt.Printf("完成主机扫描: %s\n", host.Name)
	}

	// 任务完成
	s.tasks[taskIndex].Status = "已完成"
	s.tasks[taskIndex].Progress = 1.0
	now := time.Now()
	s.tasks[taskIndex].CompletedAt = &now

	// 生成模拟扫描结果
	s.tasks[taskIndex].Results = s.generateMockResults(task.HostIDs)

	// 更新选中任务的引用
	s.updateSelectedTaskReference(task.ID)

	s.saveTasks()

	// 在主线程中更新UI
	go func() {
		s.taskList.Refresh()
		s.updateButtonStates()
		s.updateStatus(fmt.Sprintf("任务 '%s' 执行完成", task.Name))
	}()

	fmt.Printf("任务执行完成: %s\n", taskID)
}

// updateSelectedTaskReference 更新选中任务的引用
func (s *ScanTab) updateSelectedTaskReference(taskID string) {
	if s.selectedTask != nil && s.selectedTask.ID == taskID {
		// 找到更新后的任务并更新引用
		for i, task := range s.tasks {
			if task.ID == taskID {
				s.selectedTask = &s.tasks[i]
				fmt.Printf("更新选中任务引用: %s (状态: %s)\n", s.selectedTask.Name, s.selectedTask.Status)
				break
			}
		}
	}
}

// generateMockResults 生成模拟扫描结果
func (s *ScanTab) generateMockResults(hostIDs []string) []data.ScanResult {
	results := make([]data.ScanResult, 0, len(hostIDs))

	for _, hostID := range hostIDs {
		// 查找主机信息
		var hostName = hostID
		for _, host := range s.hosts {
			if host.ID == hostID {
				hostName = host.Name
				break
			}
		}

		// 生成模拟结果
		result := data.ScanResult{
			ID:        fmt.Sprintf("result_%s_%d", hostID, time.Now().Unix()),
			HostID:    hostID,
			HostName:  hostName,
			Status:    "已完成",
			StartTime: time.Now().Add(-5 * time.Minute),
			EndTime:   time.Now(),
			CheckResults: []data.BaselineCheckResult{
				{
					ID:          "password_policy",
					CheckName:   "密码策略检查",
					Category:    "账户安全",
					Status:      "通过",
					Score:       90,
					Risk:        "低",
					Description: "密码策略配置正确",
					Details:     "密码长度要求: 8位以上, 复杂度要求: 已启用",
					CheckedAt:   time.Now(),
				},
				{
					ID:          "ssh_config",
					CheckName:   "SSH配置检查",
					Category:    "网络安全",
					Status:      "警告",
					Score:       70,
					Risk:        "中",
					Description: "SSH配置存在安全风险",
					Details:     "建议禁用root直接登录",
					CheckedAt:   time.Now(),
				},
				{
					ID:          "firewall_status",
					CheckName:   "防火墙状态检查",
					Category:    "网络安全",
					Status:      "通过",
					Score:       95,
					Risk:        "低",
					Description: "防火墙已启用",
					Details:     "iptables服务正在运行",
					CheckedAt:   time.Now(),
				},
			},
			PassedChecks:  2,
			WarningChecks: 1,
			FailedChecks:  0,
			SkippedChecks: 0,
			TotalScore:    255,
			MaxScore:      300,
		}

		results = append(results, result)
	}

	return results
}

// exportTask 导出任务结果
func (s *ScanTab) exportTask(task data.ScanTask) {
	if task.Status != "已完成" {
		dialog.ShowInformation("提示", "只能导出已完成的任务结果", s.window)
		return
	}

	s.updateStatus(fmt.Sprintf("导出任务 '%s' 的结果...", task.Name))
	// TODO: 实现导出功能
}

// updateButtonStates 更新按钮状态
func (s *ScanTab) updateButtonStates() {
	if s.selectedTask == nil {
		fmt.Printf("更新按钮状态: 无选中任务，禁用所有按钮\n")
		s.editTaskBtn.Disable()
		s.deleteTaskBtn.Disable()
		s.startStopBtn.Disable()
		s.detailBtn.Disable()
		return
	}

	fmt.Printf("更新按钮状态: 选中任务 %s (状态: %s)\n", s.selectedTask.Name, s.selectedTask.Status)

	// 详情按钮始终可用（只要选中了任务）
	s.detailBtn.Enable()

	// 根据任务状态更新按钮
	switch s.selectedTask.Status {
	case "待执行":
		s.editTaskBtn.Enable()
		s.deleteTaskBtn.Enable()
		s.startStopBtn.Enable()
		s.startStopBtn.SetText("启动")
	case "运行中":
		s.editTaskBtn.Disable()
		s.deleteTaskBtn.Disable()
		s.startStopBtn.Enable()
		s.startStopBtn.SetText("停止")
	case "已完成", "已停止", "失败":
		s.editTaskBtn.Disable()
		s.deleteTaskBtn.Enable()
		s.startStopBtn.Disable()
	}
}

// loadTasks 加载任务数据
func (s *ScanTab) loadTasks() {
	// 从存储加载任务数据
	if tasks, err := s.storage.LoadTasks(); err == nil {
		s.tasks = tasks
		fmt.Printf("从文件加载任务数据: %d个任务\n", len(s.tasks))
	} else {
		fmt.Printf("加载任务数据失败: %v\n", err)
		s.tasks = []data.ScanTask{} // 初始化为空数组
	}
}

// saveTasks 保存任务数据
func (s *ScanTab) saveTasks() {
	fmt.Printf("保存任务数据: 当前任务数量 %d\n", len(s.tasks))

	// 将任务数据保存到JSON文件
	if err := s.storage.SaveTasks(s.tasks); err != nil {
		fmt.Printf("保存任务数据失败: %v\n", err)
	} else {
		fmt.Printf("任务数据已保存到文件\n")
	}
}

// RefreshData 刷新数据
func (s *ScanTab) RefreshData(hosts []data.HostInfo, groups []data.HostGroup) {
	// 更新为最新的数据
	s.hosts = hosts
	s.groups = groups
	fmt.Printf("刷新数据: %d个主机, %d个主机组\n", len(s.hosts), len(s.groups))
}

// UpdateProgress 更新进度
func (s *ScanTab) UpdateProgress(progress data.ScanProgress) {
	// TODO: 更新任务进度显示
}

// refreshHostData 实时刷新主机和主机组数据
func (s *ScanTab) refreshHostData() {
	// 如果有应用实例引用，通知应用刷新数据
	if s.app != nil {
		s.app.loadData() // 直接调用应用的数据加载方法
		s.hosts = s.app.hosts
		s.groups = s.app.groups
		return
	}

	// 备用方案：直接从存储中获取最新的主机数据
	if hosts, err := s.storage.LoadHosts(); err == nil && len(hosts) > 0 {
		s.hosts = hosts
	}

	// 从存储中获取最新的主机组数据
	if groups, err := s.storage.LoadGroups(); err == nil && len(groups) > 0 {
		s.groups = groups
	}
}


