package ui

import (
	"fmt"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"lcheck/data"
)

// HostTab 主机管理选项卡
type HostTab struct {
	storage      *data.Storage
	updateStatus func(string)
	window       fyne.Window

	// UI组件
	container *container.Split

	// 列表组件
	hostList  *widget.List
	groupList *widget.List

	// 工具栏按钮
	addHostBtn  *widget.Button
	addGroupBtn *widget.Button
	editBtn     *widget.Button
	deleteBtn   *widget.Button
	refreshBtn  *widget.Button

	// 数据
	hosts         []data.HostInfo
	groups        []data.HostGroup
	selectedHost  *data.HostInfo
	selectedGroup *data.HostGroup
}

// NewHostTab 创建主机管理选项卡
func NewHostTab(storage *data.Storage, scanner interface{}, updateStatus func(string)) *HostTab {
	tab := &HostTab{
		storage:      storage,
		updateStatus: updateStatus,
		hosts:        make([]data.HostInfo, 0),
		groups:       make([]data.HostGroup, 0),
	}

	tab.initializeComponents()
	tab.loadData()
	return tab
}

// initializeComponents 初始化组件
func (h *HostTab) initializeComponents() {
	// 创建工具栏按钮
	h.addHostBtn = widget.NewButton("添加主机", h.showAddHostDialog)
	h.addGroupBtn = widget.NewButton("添加主机组", h.showAddGroupDialog)
	h.editBtn = widget.NewButton("编辑", h.editSelected)
	h.deleteBtn = widget.NewButton("删除", h.deleteSelected)
	h.refreshBtn = widget.NewButton("刷新", h.refreshData)

	// 初始状态禁用编辑和删除按钮
	h.editBtn.Disable()
	h.deleteBtn.Disable()

	// 创建主机列表
	h.hostList = widget.NewList(
		func() int {
			return len(h.hosts)
		},
		func() fyne.CanvasObject {
			seqLabel := widget.NewLabel("1.")
			nameLabel := widget.NewLabel("主机名")
			ipLabel := widget.NewLabel("用户@IP:端口")

			// 设置标签样式
			seqLabel.TextStyle = fyne.TextStyle{Bold: true}
			nameLabel.TextStyle = fyne.TextStyle{Bold: true}
			ipLabel.TextStyle = fyne.TextStyle{}

			// 创建紧凑的水平布局：序号 + 主机信息
			content := container.NewHBox(
				container.NewPadded(seqLabel),
				container.NewVBox(nameLabel, ipLabel),
			)

			return content
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id < len(h.hosts) {
				host := h.hosts[id]
				containerObj := obj.(*fyne.Container)
				seqContainer := containerObj.Objects[0].(*fyne.Container)
				infoContainer := containerObj.Objects[1].(*fyne.Container)

				seqLabel := seqContainer.Objects[0].(*widget.Label)
				nameLabel := infoContainer.Objects[0].(*widget.Label)
				ipLabel := infoContainer.Objects[1].(*widget.Label)

				seqLabel.SetText(fmt.Sprintf("%d.", id+1))
				nameLabel.SetText(host.Name)
				ipLabel.SetText(fmt.Sprintf("%s@%s:%s", host.Username, host.Host, host.Port))
			}
		},
	)

	// 设置主机列表选择回调
	h.hostList.OnSelected = func(id widget.ListItemID) {
		if id < len(h.hosts) {
			h.selectedHost = &h.hosts[id]
			h.selectedGroup = nil
			// 清除主机组列表的选择
			h.groupList.UnselectAll()
			h.editBtn.Enable()
			h.deleteBtn.Enable()
		}
	}

	// 创建主机组列表
	h.groupList = widget.NewList(
		func() int {
			return len(h.groups)
		},
		func() fyne.CanvasObject {
			seqLabel := widget.NewLabel("1.")
			nameLabel := widget.NewLabel("主机组名")
			countLabel := widget.NewLabel("主机数量")

			// 设置标签样式
			seqLabel.TextStyle = fyne.TextStyle{Bold: true}
			nameLabel.TextStyle = fyne.TextStyle{Bold: true}
			countLabel.TextStyle = fyne.TextStyle{}

			// 创建紧凑的水平布局：序号 + 组信息
			content := container.NewHBox(
				container.NewPadded(seqLabel),
				container.NewVBox(nameLabel, countLabel),
			)

			return content
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id < len(h.groups) {
				group := h.groups[id]
				containerObj := obj.(*fyne.Container)
				seqContainer := containerObj.Objects[0].(*fyne.Container)
				infoContainer := containerObj.Objects[1].(*fyne.Container)

				seqLabel := seqContainer.Objects[0].(*widget.Label)
				nameLabel := infoContainer.Objects[0].(*widget.Label)
				countLabel := infoContainer.Objects[1].(*widget.Label)

				seqLabel.SetText(fmt.Sprintf("%d.", id+1))
				nameLabel.SetText(group.Name)
				countLabel.SetText(fmt.Sprintf("%d台主机", len(group.Hosts)))
			}
		},
	)

	// 设置主机组列表选择回调
	h.groupList.OnSelected = func(id widget.ListItemID) {
		if id < len(h.groups) {
			h.selectedGroup = &h.groups[id]
			h.selectedHost = nil
			// 清除主机列表的选择
			h.hostList.UnselectAll()
			h.editBtn.Enable()
			h.deleteBtn.Enable()
		}
	}
}

// Create 创建选项卡内容
func (h *HostTab) Create() fyne.CanvasObject {
	// 左侧：主机列表 - 使用更大的容器
	hostListContainer := container.NewBorder(
		widget.NewLabel("主机列表"),         // 顶部标题
		nil,                             // 底部
		nil,                             // 左侧
		nil,                             // 右侧
		container.NewScroll(h.hostList), // 中心内容
	)

	// 中间：主机组列表 - 使用更大的容器
	groupListContainer := container.NewBorder(
		widget.NewLabel("主机组列表"),         // 顶部标题
		nil,                              // 底部
		nil,                              // 左侧
		nil,                              // 右侧
		container.NewScroll(h.groupList), // 中心内容
	)

	// 左侧区域：两个列表并排，给更多空间
	leftArea := container.NewHSplit(hostListContainer, groupListContainer)
	leftArea.SetOffset(0.5) // 50-50分割

	// 右侧：操作按钮区域 - 紧凑布局
	rightArea := container.NewVBox(
		widget.NewCard("", "操作", container.NewVBox(
			h.addHostBtn,
			h.addGroupBtn,
			widget.NewSeparator(),
			h.editBtn,
			h.deleteBtn,
			widget.NewSeparator(),
			h.refreshBtn,
		)),
	)

	// 主布局：左侧列表区域 + 右侧按钮区域
	// 给列表更多空间，按钮区域更小
	h.container = container.NewHSplit(leftArea, rightArea)
	h.container.SetOffset(0.8) // 左侧80%，右侧20%

	return h.container
}

// 对话框和操作方法

// showAddHostDialog 显示添加主机对话框
func (h *HostTab) showAddHostDialog() {
	dialog := NewAddHostDialog(h.window, h.storage, h.updateStatus, h.refreshData)
	dialog.Show()
}

// showAddGroupDialog 显示添加主机组对话框
func (h *HostTab) showAddGroupDialog() {
	dialog := NewAddGroupDialog(h.window, h.storage, h.updateStatus, h.refreshData)
	dialog.Show()
}

// editSelected 编辑选中项目
func (h *HostTab) editSelected() {
	if h.selectedHost != nil {
		dialog := NewAddHostDialog(h.window, h.storage, h.updateStatus, h.refreshData)
		dialog.ShowEdit(*h.selectedHost)
	} else if h.selectedGroup != nil {
		dialog := NewAddGroupDialog(h.window, h.storage, h.updateStatus, h.refreshData)
		dialog.ShowEdit(*h.selectedGroup)
	}
}

// deleteSelected 删除选中项目
func (h *HostTab) deleteSelected() {
	var title, message string
	var deleteFunc func() error

	if h.selectedHost != nil {
		title = "删除主机"
		message = fmt.Sprintf("确定要删除主机 \"%s\" 吗？", h.selectedHost.Name)
		deleteFunc = func() error {
			return h.storage.DeleteHost(h.selectedHost.ID)
		}
	} else if h.selectedGroup != nil {
		title = "删除主机组"
		message = fmt.Sprintf("确定要删除主机组 \"%s\" 吗？", h.selectedGroup.Name)
		deleteFunc = func() error {
			return h.storage.DeleteGroup(h.selectedGroup.ID)
		}
	}

	if deleteFunc != nil {
		dialog.ShowConfirm(title, message, func(confirmed bool) {
			if confirmed {
				if err := deleteFunc(); err != nil {
					h.updateStatus("删除失败: " + err.Error())
					dialog.ShowError(fmt.Errorf("删除失败: %v", err), h.window)
				} else {
					h.updateStatus("删除成功")
					h.refreshData()
				}
			}
		}, h.window)
	}
}

// refreshData 刷新数据
func (h *HostTab) refreshData() {
	h.loadData()
	if h.hostList != nil {
		h.hostList.UnselectAll()
		h.hostList.Refresh()
	}
	if h.groupList != nil {
		h.groupList.UnselectAll()
		h.groupList.Refresh()
	}
	h.selectedHost = nil
	h.selectedGroup = nil
	h.editBtn.Disable()
	h.deleteBtn.Disable()
}

// loadData 加载数据
func (h *HostTab) loadData() {
	// 加载主机数据
	if hosts, err := h.storage.LoadHosts(); err == nil {
		h.hosts = hosts
	}

	// 加载主机组数据
	if groups, err := h.storage.LoadGroups(); err == nil {
		h.groups = groups
	}
}

// SetWindow 设置窗口引用
func (h *HostTab) SetWindow(window fyne.Window) {
	h.window = window
}

// RefreshData 刷新数据
func (h *HostTab) RefreshData(hosts []data.HostInfo, groups []data.HostGroup) {
	h.hosts = hosts
	h.groups = groups
	if h.hostList != nil {
		h.hostList.UnselectAll()
		h.hostList.Refresh()
	}
	if h.groupList != nil {
		h.groupList.UnselectAll()
		h.groupList.Refresh()
	}
	// 清除选择状态
	h.selectedHost = nil
	h.selectedGroup = nil
	h.editBtn.Disable()
	h.deleteBtn.Disable()
}
