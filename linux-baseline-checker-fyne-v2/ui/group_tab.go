package ui

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"lcheck/data"
)

// GroupTab 主机组管理选项卡
type GroupTab struct {
	storage      *data.Storage
	updateStatus func(string)

	// UI组件
	container *container.Split
	groupList *widget.List
	groups    []data.HostGroup
}

// NewGroupTab 创建主机组选项卡
func NewGroupTab(storage *data.Storage, updateStatus func(string)) *GroupTab {
	return &GroupTab{
		storage:      storage,
		updateStatus: updateStatus,
		groups:       make([]data.HostGroup, 0),
	}
}

// Create 创建选项卡内容
func (g *GroupTab) Create() fyne.CanvasObject {
	// 创建主机组列表
	g.groupList = widget.NewList(
		func() int {
			return len(g.groups)
		},
		func() fyne.CanvasObject {
			return widget.NewLabel("主机组")
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id < len(g.groups) {
				group := g.groups[id]
				label := obj.(*widget.Label)
				label.SetText(group.Name)
			}
		},
	)

	// 左侧：创建主机组表单
	leftPanel := container.NewVBox(
		widget.NewCard("创建主机组", "", container.NewVBox(
			widget.NewLabel("功能开发中..."),
		)),
	)

	// 右侧：主机组列表
	rightPanel := container.NewVBox(
		widget.NewCard("主机组列表", "", container.NewVBox(
			g.groupList,
		)),
	)

	g.container = container.NewHSplit(leftPanel, rightPanel)
	g.container.SetOffset(0.4)

	return g.container
}

// RefreshData 刷新数据
func (g *GroupTab) RefreshData(groups []data.HostGroup) {
	g.groups = groups
	if g.groupList != nil {
		g.groupList.Refresh()
	}
}
