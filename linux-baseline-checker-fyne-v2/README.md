# Lcheck v3.2.0 - Linux远程基线核查工具

## 🎯 项目简介

Lcheck是一个使用Go语言和Fyne GUI框架开发的Linux远程基线安全检查工具，采用了**全新的界面设计**，更加简洁直观，符合现代桌面软件的交互习惯。

## ✨ 主要特性

### 🏗️ 模块化架构
- **清晰的分层设计**: 核心功能、数据管理、UI界面完全分离
- **可扩展性**: 易于添加新的检查项目和功能模块
- **可维护性**: 代码结构清晰，职责分明
- **可测试性**: 每个模块都可以独立测试

### 🔧 核心功能模块

#### 📁 项目结构
```
lcheck/
├── main.go                 # 程序入口
├── core/                   # 核心功能模块
│   ├── ssh.go             # SSH连接管理
│   ├── baseline.go        # 基线检查规则引擎
│   └── scanner.go         # 扫描引擎
├── data/                   # 数据管理模块
│   ├── models.go          # 数据模型定义
│   └── storage.go         # 数据存储管理
├── ui/                     # 用户界面模块
│   ├── app.go             # 主应用框架
│   ├── host_tab.go        # 主机管理界面
│   ├── group_tab.go       # 主机组管理界面
│   ├── scan_tab.go        # 扫描执行界面
│   └── report_tab.go      # 报告查看界面
├── utils/                  # 工具函数模块
│   └── helpers.go         # 辅助函数
├── config/                 # 配置管理模块
│   └── config.go          # 应用配置管理
└── build-windows.sh       # Windows构建脚本
```

#### 🛡️ 安全检查引擎
**12+ 专业安全检查项目**，涵盖：

**账户安全类**:
- 密码策略检查 (密码长度、有效期、复杂度)
- root账户安全检查
- 空密码账户检查

**SSH安全类**:
- SSH配置安全检查
- SSH协议版本检查

**网络安全类**:
- 防火墙状态检查
- 网络服务安全检查

**系统安全类**:
- 系统更新检查
- 内核参数安全检查

**文件系统安全类**:
- 关键文件权限检查
- SUID/SGID文件检查

**日志审计类**:
- 日志配置检查
- 审计服务检查

### 🖥️ 原生桌面体验
- **真正的原生应用**: 使用Fyne框架，提供原生Windows桌面体验
- **专业界面设计**: 选项卡式布局，功能分区清晰
- **实时进度显示**: 扫描进度实时更新
- **数据持久化**: 本地JSON存储，支持数据导入导出

## 📦 安装和使用

### 系统要求
- Windows 7/8/10/11 (64位)
- 网络连接到目标Linux主机
- 目标主机需开启SSH服务

### 快速开始
1. 下载对应版本：
   - `Lcheck-fyne-x64.exe` (48MB) - Fyne打包版，功能完整
   - `Lcheck-go-x64.exe` (24MB) - 纯Go版，体积更小
2. 双击运行即可
3. 首次运行会在程序目录创建数据文件

### 数据文件说明
程序会在运行目录创建以下文件：
- `hosts.json` - 保存的主机信息
- `groups.json` - 主机组信息
- `config.json` - 应用配置
- `scans/` - 扫描结果目录
- `exports/` - 导出文件目录

## 🚀 功能使用指南

### 1. 主机管理 🖥️
- **添加主机**: 填写SSH连接信息并保存
- **测试连接**: 验证SSH连接是否正常
- **获取信息**: 收集远程主机详细系统信息
- **批量管理**: 支持主机信息的导入导出

### 2. 主机组管理 🏢
- **创建分组**: 为相关主机创建逻辑分组
- **批量操作**: 对整个主机组执行批量扫描
- **灵活管理**: 动态添加、删除主机组成员

### 3. 执行扫描 🔍
- **单主机扫描**: 选择单个主机进行详细安全检查
- **批量扫描**: 选择主机组进行并发扫描
- **进度监控**: 实时显示扫描进度和状态
- **并发控制**: 可配置扫描并发数量

### 4. 扫描报告 📊
- **详细报告**: 查看完整的安全检查结果
- **统计分析**: 风险等级统计和趋势分析
- **结果导出**: 支持JSON、CSV格式导出
- **历史记录**: 查看历史扫描记录

## 🔧 开发和构建

### 环境要求
- Go 1.21+
- GCC MinGW-w64 (用于Windows交叉编译)
- Fyne v2.6+

### 构建Windows版本
```bash
# 克隆项目
git clone <repository>
cd lcheck

# 构建Fyne打包版本（推荐）
./build-windows-fyne.sh

# 或构建纯Go版本（更小体积）
./build-windows-go.sh
```

### 开发模式运行
```bash
# 安装依赖
go mod tidy

# 运行开发版本
go run .
```

## 🎨 技术架构

### 核心设计原则
1. **单一职责**: 每个模块只负责一个特定功能
2. **依赖注入**: 通过接口实现模块间的松耦合
3. **配置驱动**: 所有配置都可以通过配置文件调整
4. **错误处理**: 完善的错误处理和日志记录

### 技术栈
- **GUI框架**: Fyne v2.6 (原生跨平台GUI)
- **SSH库**: golang.org/x/crypto/ssh
- **数据存储**: JSON文件 (轻量级、易于备份)
- **并发处理**: Go协程 + 通道
- **配置管理**: 结构化配置系统

### 扩展性设计
- **检查项目**: 在 `core/baseline.go` 中添加新的检查函数
- **数据模型**: 在 `data/models.go` 中扩展数据结构
- **UI组件**: 在 `ui/` 目录下添加新的界面模块
- **工具函数**: 在 `utils/helpers.go` 中添加通用函数

## 🔒 安全说明

- SSH密码以明文形式存储在本地JSON文件中
- 建议在安全的环境中使用
- 支持SSH密钥认证（推荐）
- 定期备份配置和数据文件

## 📝 更新日志

### v3.2.0 (模块化重构版本)
- ✅ 完全模块化的架构重构
- ✅ 12+ 专业安全检查项目
- ✅ 原生Fyne GUI界面
- ✅ 批量扫描和进度监控
- ✅ 配置管理和数据持久化
- ✅ 完善的错误处理和日志
- ✅ 支持Windows交叉编译

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目采用MIT许可证。
