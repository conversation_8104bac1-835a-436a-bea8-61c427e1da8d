package data

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// Storage 数据存储管理器
type Storage struct {
	dataDir string
	mutex   sync.RWMutex
}

// NewStorage 创建存储管理器
func NewStorage(dataDir string) *Storage {
	if dataDir == "" {
		// 获取程序运行目录
		if exePath, err := os.Executable(); err == nil {
			dataDir = filepath.Dir(exePath)
		} else {
			dataDir = "."
		}
	}

	// 确保数据目录存在
	os.MkdirAll(dataDir, 0755)

	return &Storage{
		dataDir: dataDir,
	}
}

// GetDataDir 获取数据目录
func (s *Storage) GetDataDir() string {
	return s.dataDir
}

// 主机管理
func (s *Storage) SaveHosts(hosts []HostInfo) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	filePath := filepath.Join(s.dataDir, "hosts.json")
	data, err := json.MarshalIndent(hosts, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化主机数据失败: %v", err)
	}

	return ioutil.WriteFile(filePath, data, 0644)
}

func (s *Storage) LoadHosts() ([]HostInfo, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	filePath := filepath.Join(s.dataDir, "hosts.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return []HostInfo{}, nil
	}

	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取主机文件失败: %v", err)
	}

	var hosts []HostInfo
	if err := json.Unmarshal(data, &hosts); err != nil {
		return nil, fmt.Errorf("解析主机数据失败: %v", err)
	}

	return hosts, nil
}

func (s *Storage) SaveHost(host HostInfo) error {
	hosts, err := s.LoadHosts()
	if err != nil {
		return err
	}

	// 查找是否已存在
	found := false
	for i, h := range hosts {
		if h.ID == host.ID {
			hosts[i] = host
			hosts[i].UpdatedAt = time.Now()
			found = true
			break
		}
	}

	// 如果不存在则添加
	if !found {
		host.CreatedAt = time.Now()
		host.UpdatedAt = time.Now()
		hosts = append(hosts, host)
	}

	return s.SaveHosts(hosts)
}

func (s *Storage) DeleteHost(hostID string) error {
	hosts, err := s.LoadHosts()
	if err != nil {
		return err
	}

	// 过滤掉要删除的主机
	filteredHosts := make([]HostInfo, 0)
	for _, host := range hosts {
		if host.ID != hostID {
			filteredHosts = append(filteredHosts, host)
		}
	}

	return s.SaveHosts(filteredHosts)
}

func (s *Storage) GetHost(hostID string) (*HostInfo, error) {
	hosts, err := s.LoadHosts()
	if err != nil {
		return nil, err
	}

	for _, host := range hosts {
		if host.ID == hostID {
			return &host, nil
		}
	}

	return nil, fmt.Errorf("主机不存在: %s", hostID)
}

// 主机组管理
func (s *Storage) SaveGroups(groups []HostGroup) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	filePath := filepath.Join(s.dataDir, "groups.json")
	data, err := json.MarshalIndent(groups, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化主机组数据失败: %v", err)
	}

	return ioutil.WriteFile(filePath, data, 0644)
}

func (s *Storage) LoadGroups() ([]HostGroup, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	filePath := filepath.Join(s.dataDir, "groups.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return []HostGroup{}, nil
	}

	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取主机组文件失败: %v", err)
	}

	var groups []HostGroup
	if err := json.Unmarshal(data, &groups); err != nil {
		return nil, fmt.Errorf("解析主机组数据失败: %v", err)
	}

	return groups, nil
}

func (s *Storage) SaveGroup(group HostGroup) error {
	groups, err := s.LoadGroups()
	if err != nil {
		return err
	}

	// 查找是否已存在
	found := false
	for i, g := range groups {
		if g.ID == group.ID {
			groups[i] = group
			groups[i].UpdatedAt = time.Now()
			found = true
			break
		}
	}

	// 如果不存在则添加
	if !found {
		group.CreatedAt = time.Now()
		group.UpdatedAt = time.Now()
		groups = append(groups, group)
	}

	return s.SaveGroups(groups)
}

func (s *Storage) DeleteGroup(groupID string) error {
	groups, err := s.LoadGroups()
	if err != nil {
		return err
	}

	// 过滤掉要删除的主机组
	filteredGroups := make([]HostGroup, 0)
	for _, group := range groups {
		if group.ID != groupID {
			filteredGroups = append(filteredGroups, group)
		}
	}

	return s.SaveGroups(filteredGroups)
}

func (s *Storage) GetGroup(groupID string) (*HostGroup, error) {
	groups, err := s.LoadGroups()
	if err != nil {
		return nil, err
	}

	for _, group := range groups {
		if group.ID == groupID {
			return &group, nil
		}
	}

	return nil, fmt.Errorf("主机组不存在: %s", groupID)
}

// 扫描结果管理
func (s *Storage) SaveScanResult(result ScanResult) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 按日期创建目录
	dateDir := result.StartTime.Format("2006-01-02")
	scanDir := filepath.Join(s.dataDir, "scans", dateDir)
	os.MkdirAll(scanDir, 0755)

	// 保存单个扫描结果
	filename := fmt.Sprintf("scan_%s_%s.json", result.HostID, result.StartTime.Format("150405"))
	filePath := filepath.Join(scanDir, filename)

	data, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化扫描结果失败: %v", err)
	}

	return ioutil.WriteFile(filePath, data, 0644)
}

func (s *Storage) SaveBatchScanResult(result BatchScanResult) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 按日期创建目录
	dateDir := result.StartTime.Format("2006-01-02")
	scanDir := filepath.Join(s.dataDir, "batch_scans", dateDir)
	os.MkdirAll(scanDir, 0755)

	// 保存批量扫描结果
	filename := fmt.Sprintf("batch_scan_%s_%s.json", result.GroupID, result.StartTime.Format("150405"))
	filePath := filepath.Join(scanDir, filename)

	data, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化批量扫描结果失败: %v", err)
	}

	return ioutil.WriteFile(filePath, data, 0644)
}

// 导出功能
func (s *Storage) ExportResults(results []ScanResult, filename string) error {
	if filename == "" {
		timestamp := time.Now().Format("20060102_150405")
		filename = fmt.Sprintf("scan_results_%s.json", timestamp)
	}

	filePath := filepath.Join(s.dataDir, "exports", filename)
	os.MkdirAll(filepath.Dir(filePath), 0755)

	data, err := json.MarshalIndent(results, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化导出数据失败: %v", err)
	}

	return ioutil.WriteFile(filePath, data, 0644)
}

// 配置管理
func (s *Storage) SaveConfig(config AppConfig) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	filePath := filepath.Join(s.dataDir, "config.json")
	config.UpdatedAt = time.Now()

	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	return ioutil.WriteFile(filePath, data, 0644)
}

func (s *Storage) LoadConfig() (*AppConfig, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	filePath := filepath.Join(s.dataDir, "config.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		// 返回默认配置
		return &AppConfig{
			Version:        "3.2.0",
			DataDir:        s.dataDir,
			LogLevel:       "INFO",
			MaxConcurrency: 5,
			SSHTimeout:     30,
			ScanTimeout:    300,
			AutoSave:       true,
			BackupEnabled:  false,
			BackupInterval: 24,
			Theme:          "default",
			Language:       "zh-CN",
			WindowWidth:    1200,
			WindowHeight:   800,
			CustomSettings: make(map[string]string),
			UpdatedAt:      time.Now(),
		}, nil
	}

	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	var config AppConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置数据失败: %v", err)
	}

	return &config, nil
}

// 统计信息
func (s *Storage) GetStatistics() (*Statistics, error) {
	hosts, err := s.LoadHosts()
	if err != nil {
		return nil, err
	}

	groups, err := s.LoadGroups()
	if err != nil {
		return nil, err
	}

	stats := &Statistics{
		TotalHosts:  len(hosts),
		TotalGroups: len(groups),
		UpdatedAt:   time.Now(),
	}

	// TODO: 计算更多统计信息

	return stats, nil
}

// 清理功能
func (s *Storage) CleanupOldScans(days int) error {
	cutoff := time.Now().AddDate(0, 0, -days)

	scanDirs := []string{
		filepath.Join(s.dataDir, "scans"),
		filepath.Join(s.dataDir, "batch_scans"),
	}

	for _, scanDir := range scanDirs {
		if _, err := os.Stat(scanDir); os.IsNotExist(err) {
			continue
		}

		err := filepath.Walk(scanDir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			if !info.IsDir() && info.ModTime().Before(cutoff) {
				return os.Remove(path)
			}

			return nil
		})

		if err != nil {
			return fmt.Errorf("清理旧扫描文件失败: %v", err)
		}
	}

	return nil
}

// 任务管理
func (s *Storage) SaveTasks(tasks []ScanTask) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	filePath := filepath.Join(s.dataDir, "tasks.json")
	data, err := json.MarshalIndent(tasks, "", "  ")
	if err != nil {
		return err
	}

	return ioutil.WriteFile(filePath, data, 0644)
}

func (s *Storage) LoadTasks() ([]ScanTask, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	filePath := filepath.Join(s.dataDir, "tasks.json")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return []ScanTask{}, nil
	}

	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	var tasks []ScanTask
	if err := json.Unmarshal(data, &tasks); err != nil {
		return nil, err
	}

	return tasks, nil
}
